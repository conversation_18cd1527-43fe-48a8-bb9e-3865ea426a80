#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键打包脚本：将Python文件打包成exe可执行文件
功能：使用PyInstaller打包，无控制台窗口，单文件输出
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def install_pyinstaller():
    """
    检查并安装PyInstaller
    """
    try:
        import PyInstaller
        print("✅ PyInstaller已安装")
        return True
    except ImportError:
        print("⚠️ PyInstaller未安装，正在自动安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ PyInstaller安装失败：{e}")
            return False

def clean_build_files():
    """
    清理之前的打包文件
    """
    print("🧹 清理之前的打包文件...")
    
    # 要清理的目录和文件
    cleanup_items = [
        "build",
        "dist", 
        "__pycache__",
        "*.spec"
    ]
    
    for item in cleanup_items:
        if item.startswith("*."):
            # 处理通配符文件
            import glob
            for file in glob.glob(item):
                try:
                    os.remove(file)
                    print(f"   删除文件: {file}")
                except:
                    pass
        else:
            # 处理目录
            if os.path.exists(item):
                try:
                    shutil.rmtree(item)
                    print(f"   删除目录: {item}")
                except:
                    pass
    
    print("✅ 清理完成")

def package_to_exe(python_file):
    """
    将Python文件打包成exe
    """
    if not os.path.exists(python_file):
        print(f"❌ 找不到Python文件：{python_file}")
        return False
    
    print(f"📦 开始打包：{python_file}")
    
    # 获取文件名（不含扩展名）
    file_name = Path(python_file).stem
    
    # PyInstaller打包命令参数
    cmd = [
        "pyinstaller",
        "--onefile",           # 打包成单个exe文件
        "--windowed",          # 无控制台窗口（对于GUI程序）
        "--noconsole",         # 不显示控制台
        "--clean",             # 清理临时文件
        f"--name={file_name}", # 指定输出文件名
        python_file            # 源Python文件
    ]
    
    print("🔄 执行打包命令...")
    print(f"   命令: {' '.join(cmd)}")
    
    try:
        # 执行打包命令
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 打包成功！")
            
            # 检查输出文件
            exe_file = f"dist/{file_name}.exe"
            if os.path.exists(exe_file):
                file_size = os.path.getsize(exe_file)
                file_size_mb = file_size / (1024 * 1024)
                print(f"📁 输出文件：{exe_file}")
                print(f"📊 文件大小：{file_size_mb:.2f} MB")
                
                # 移动exe文件到当前目录
                target_exe = f"{file_name}.exe"
                if os.path.exists(target_exe):
                    os.remove(target_exe)
                shutil.move(exe_file, target_exe)
                print(f"📦 最终文件：{target_exe}")
                
                return True
            else:
                print("❌ 未找到输出的exe文件")
                return False
        else:
            print("❌ 打包失败！")
            print("错误输出：")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 打包过程中出错：{e}")
        return False

def main():
    """
    主函数：一键打包流程
    """
    print("=" * 60)
    print("🚀 Python文件一键打包工具")
    print("=" * 60)
    
    # 目标Python文件
    target_file = "temu批量开通JIT加库存.py"
    
    print(f"🎯 目标文件：{target_file}")
    
    # 步骤1：检查并安装PyInstaller
    print("\n📋 步骤1：检查PyInstaller...")
    if not install_pyinstaller():
        print("❌ 无法安装PyInstaller，打包终止")
        input("按回车键退出...")
        return
    
    # 步骤2：清理之前的打包文件
    print("\n📋 步骤2：清理打包环境...")
    clean_build_files()
    
    # 步骤3：执行打包
    print("\n📋 步骤3：开始打包...")
    success = package_to_exe(target_file)
    
    # 步骤4：清理临时文件
    print("\n📋 步骤4：清理临时文件...")
    clean_build_files()
    
    # 打包结果
    print("\n" + "=" * 60)
    if success:
        print("🎉 打包完成！")
        print("✅ 可执行文件已生成，可以直接运行")
        print("💡 提示：请确保key.vdf授权文件与exe文件在同一目录")
    else:
        print("❌ 打包失败，请检查错误信息")
    print("=" * 60)
    
    input("按回车键退出...")

if __name__ == "__main__":
    main()
